# Bluetooth Technology Part II Study Guide (Revised)

## 1. Introduction to Bluetooth Communication Protocol (Page 1)

### Key Concepts
- **Bluetooth Communication Protocol**: A wireless technology standard for short-range communication between devices, covered in Part II of the lecture by Prof<PERSON> <PERSON><PERSON> as part of an Internet of Things (IoT) course.
- **Focus**: The lecture dives into advanced Bluetooth topics, including its history, technical specifications, architecture, and security aspects.

البلوتوث ده تقنية لاسلكية بتستخدم عشان تربط أجهزة قريبة من بعض من غير أسلاك. يعني مثلاً لو عايز تبعت صورة من موبايلك لموبايل صاحبك، أو تربط سماعة وايرلس، البلوتوث هو اللي بيعمل الحركة دي. في المحاضرة دي، هنتعمق أكتر في التفاصيل التقنية زي إزاي البلوتوث بيشتغل، تاريخه، وإزاي بيحافظ على الأمان.

---

## 2. Brief History of Bluetooth (Page 3)

### Key Concepts
- **First Formal Version (1999)**: Introduced Basic Rate (BR) for basic functionalities.
- **Enhanced Data Rate (EDR)**: Improved transmission speed by 3x, based on BR.
- **High Speed (HS, 2009)**: Utilized Wi-Fi channels for faster data transfer, based on BR/EDR.
- **Low Energy (LE)**: Introduced for energy efficiency, optimized for IoT.
- **Mesh Topology (2016–2017)**: Enabled devices to form interconnected networks, enhancing IoT applications.

تاريخ البلوتوث باختصار: البلوتوث بدأ سنة 1999 بإصدار بسيط كان بيوفر وظايف أساسية زي ربط سماعة أو كيبورد. بعدين، طوروه عشان يبقى أسرع (EDR) يعني بيبعت بيانات بسرعة تلات أضعاف. سنة 2009، دخلوا خاصية (HS) بتستخدم الواي فاي عشان تنقل بيانات أسرع. بعد كده، ركزوا على (LE) عشان الأجهزة اللي محتاجة توفر طاقة، زي الساعات الذكية. وفي 2016-2017، جابوا فكرة الميش (Mesh) عشان الأجهزة ترتبط مع بعض زي شبكة عنكبوت.

---

## 3. Bluetooth BED vs. Bluetooth Low Energy (BLE) (Page 4)

### Key Concepts
- **Table: Comparison of Bluetooth BED and BLE** (Extracted from Page 4)

| **Characteristic**            | **Bluetooth BED (Prior to 4.1)** | **Bluetooth BED (4.1 onwards)** | **Bluetooth LE (Prior to 4.2)** | **Bluetooth LE (4.2 onwards)** |
|-------------------------------|----------------------------------|----------------------------------|----------------------------------|--------------------------------|
| **RF Physical Channels**      | 79 channels with 1 MHz spacing | Same as prior to 4.1            | 40 channels with 2 MHz spacing   | Same as prior to 4.2          |
| **Discovery/Connect**         | Inquiry/Paging                  | Same as prior to 4.1            | Advertising                      | Same as prior to 4.2          |
| **Number of Piconet Slaves**  | 7 (active)/255 (total)          | Same as prior to 4.1            | Unlimited                        | Same as prior to 4.2          |
| **Device Address Privacy**    | None                            | None                            | Private device addressing        | Same as prior to 4.2          |
| **Max Data Rate**             | 1–3 Mbps                        | Same as prior to 4.1            | 1 Mbps via GFSK modulation       | Same as prior to 4.2          |
| **Pairing Algorithm**         | Prior to 2.1: E21/E22/SAFER+ <br> 2.1–4.0: P-192 Elliptic Curve, HMAC-SHA-256 | P-256 Elliptic Curve, HMAC-SHA-256 | AES-128                         | P-256 Elliptic Curve, AES-CMAC |
| **Device Authentication**      | E1/SAFER                        | HMAC-SHA-256                    | AES-CCM                          | Same as prior to 4.2          |
| **Encryption Algorithm**      | E0/SAFER+                      | AES-CCM                         | AES-CCM                          | Same as prior to 4.2          |
| **Typical Range**             | 30 m                            | Same as prior to 4.1            | 50 m                             | Same as prior to 4.2          |
| **Max Output Power**          | 100 mW (20 dBm)                | Same as prior to 4.1            | 10 mW (10 dBm)                  | Same as prior to 4.2          |

الفرق بين BED وBLE: البلوتوث BED بيستخدم قنوات أكتر (79 قناة) وبيبعت بيانات أسرع (1-3 ميجا بت في الثانية)، فهو كويس للأشياء زي سماعات البلوتوث اللي بتبعت صوت. أما BLE فهو مصمم عشان يوفر طاقة، فبيستخدم قنوات أقل (40 قناة) وبيبعت بيانات بسرعة 1 ميجا بس، بس بيسمح بعدد غير محدود من الأجهزة المرتبطة. كمان، BLE فيه حماية أحسن للخصوصية عشان بيستخدم عناوين خاصة للأجهزة.

---

## 4. BLE Architecture (Pages 7, 28)

### Key Concepts
- **BLE Protocol Stack** (Figure 3, Page 7; Figure 22, Page 28):
  - **Applications**: User-level applications leveraging BLE.
  - **Generic Access Profile (GAP)**: Defines device interaction, roles, and modes.
  - **Generic Attribute Profile (GATT)**: Manages data exchange between devices.
  - **Attribute Protocol (ATT)**: Handles data attributes for GATT.
  - **Security Manager (SM)**: Manages security processes like pairing and encryption.
  - **Logical Link Control and Adaptation Protocol (L2CAP)**: Adapts data for transmission.
  - **Host Controller Interface (HCI)**: Interface between host and controller.
  - **Link Layer**: Manages physical connection and states.
  - **Direct Test Mode**: Used for testing BLE devices.
  - **Physical Layer**: Handles RF transmission.

هيكلية BLE: البلوتوث LE مقسم لجزئين رئيسيين: الـ Host (الجزء اللي بيتعامل مع التطبيقات) والـ Controller (الجزء اللي بيتعامل مع الاتصال المادي). الـ GAP بيحدد إزاي الأجهزة تكتشف بعض وتتربط، والـ GATT بيدير البيانات اللي بتتبادل بينهم. الـ Security Manager هو اللي بيخلي الاتصال آمن عن طريق التشفير وتأكيد الهوية. كل طبقة ليها دور معين زي الـ Link Layer اللي بتدير الاتصال الفعلي.

---

## 5. BLE RF Channels (Pages 8, 14)

### Key Concepts
- **RF Channels**: BLE uses 40 RF channels with 2 MHz spacing, ranging from 2402 MHz to 2480 MHz.
- **Primary Advertising Channels**: Channels 37, 38, and 39 are used for device discovery.
- **Secondary Advertisement/Data Channels**: The remaining 37 channels are used for secondary advertisements and data transfer during connections.

قنوات BLE: البلوتوث LE بيستخدم 40 قناة، كل قناة مفصولة عن التانية بـ 2 ميجا هرتز، من 2402 لحد 2480 ميجا هرتز. تلات قنوات منهم (37، 38، 39) هما اللي بيستخدموهم عشان الأجهزة تكتشف بعض. الباقي (37 قناة) بيستخدموهم لنقل البيانات أو إعلانات إضافية لما الأجهزة تتربط.

---

## 6. Link Layer States (Pages 9, 10, 11)

### Key Concepts
- **Figure 5: Link Layer States**:
  - **Standby**: Default state; no transmission or reception.
  - **Advertising**: Device sends advertising packets for discovery.
  - **Scanning**: Device listens for advertising packets.
  - **Initiating**: Scanning device attempts to connect to an advertising device.
  - **Connected**: Devices are linked, exchanging data regularly. The initiator is the **master**, and the advertiser is the **slave**.

حالات الـ Link Layer: الـ Link Layer هو اللي بيدير الحالات اللي بيمر بيها جهاز البلوتوث. فيه حالة الـ Standby، ودي زي لما الجهاز نايم، مش بيبعت ولا بيستقبل. الـ Advertising زي لما الجهاز يعلن عن نفسه عشان غيره يكتشفه. الـ Scanning هو لما جهاز تاني بيدور على الإعلانات دي. الـ Initiating هو لما جهاز يقرر يربط مع واحد بيعلن. وأخيراً الـ Connected لما الجهازين يتربطوا ويبدأوا يبعتوا بيانات لبعض. اللي بيبدأ الاتصال بيبقى الـ Master، واللي كان بيعلن بيبقى الـ Slave.

---

## 7. Observers, Broadcasters, Centrals, and Peripherals (Page 12)

### Key Concepts
- **Table 2: Comparison** (Page 12):
  - **Broadcaster**: Transmits only, no receiver needed, no bi-directional data, reduced hardware/software.
  - **Peripheral**: Supports bi-directional data, requires full BLE stack and both receiver/transmitter.
  - **Observer**: Receives only, no transmitter, no bi-directional data, reduced hardware/software.
  - **Central**: Supports bi-directional data, requires full BLE stack and both receiver/transmitter.

الأنواع الأربعة: البلوتوث LE فيه أربع أنواع للأجهزة. الـ Broadcaster بيبعت بس، مش محتاج يستقبل، فهو خفيف ومش مكلف. الـ Peripheral بيبعت ويستقبل، فمحتاج برامج وسوفتوير كامل. الـ Observer بيستقبل بس، زي الـ Broadcaster خفيف. الـ Central زي الـ Peripheral، بيبعت ويستقبل ومحتاج سوفتوير كامل.

---

## 8. Generic Access Profile (GAP) (Page 13)

### Key Concepts
- **GAP**: Defines how BLE devices interact, covering:
  - **Modes & Roles**: Broadcaster, Observer, Peripheral, Central.
  - **Advertisements**: Advertising, scanning, parameters, and data.
  - **Connection Establishment**: Initiating and accepting connections, connection parameters.
  - **Security**: Pairing, bonding, and authentication processes.

الـ GAP هو زي القوانين اللي بتحكم إزاي أجهزة البلوتوث LE بتتكلم مع بعض. بيحدد الأدوار (زي Broadcaster وCentral)، وبيقول إزاي الجهاز يعلن عن نفسه (Advertising) أو يدور على أجهزة تانية (Scanning). كمان بيدير إزاي الأجهزة تتربط مع بعض وإزاي تبقى آمنة.

---

## 9. Advertising and Scanning in BLE (Pages 15, 16, 17, 18)

### Key Concepts
- **Advertising Event** (Page 15): Device transmits the same packet on Primary Advertising Channels (37, 38, 39).
- **Passive Scanning** (Page 16): Scanner listens for advertising packets without responding.
- **Active Scanning** (Page 17): Scanner sends SCAN_REQ to request more data, Advertiser responds with SCAN_RSP.
- **Timing** (Page 18): Scanner uses a scan interval (e.g., 50 ms) and scan window (e.g., 25 ms). Advertising and scanning must overlap for discovery.

الإعلان والمسح: الـ Advertising زي لما جهاز يعلن عن نفسه بإنه يبعت إشارات على القنوات الرئيسية (37، 38، 39). الـ Passive Scanning هو لما جهاز تاني بيسمع الإعلانات دي بس من غير ما يرد. الـ Active Scanning لما الجهاز اللي بيسمع يطلب معلومات زيادة (SCAN_REQ) والجهاز اللي بيعلن يرد بمعلومات إضافية (SCAN_RSP). لازم الإعلان والمسح يحصل في نفس الوقت عشان الأجهزة تلاقي بعض.

---

## 10. Connection Procedure (Page 19)

### Key Concepts
- **Connection Process** (Figure, Page 19):
  - **Host A (Central, Master)**: Initiates connection, takes GAP Central and GATT Client roles.
  - **Host B (Peripheral, Slave)**: Advertises, accepts connection, takes GAP Peripheral and GATT Server roles.
  - **States**: Unassigned/Standby → Advertising (Host B) → Initiating (Host A) → Connecting → Connected.

إزاي الأجهزة تتربط؟: جهاز الـ Central (اللي بيبدأ الاتصال) بيدور على جهاز الـ Peripheral اللي بيعلن عن نفسه. لما يلاقيه، الـ Central بيبدأ عملية الربط (Initiating)، وبعدين يتربطوا (Connected). الـ Central بيبقى الـ Master والـ Peripheral بيبقى الـ Slave.

---

## 11. Bluetooth Security Concerns (Pages 21–27)

### Key Concepts
- **Security Concerns** (Page 21):
  - **Authentication**: Verifying device identity.
  - **Integrity**: Ensuring data is untampered.
  - **Confidentiality**: Preventing unauthorized data access.
  - **Privacy**: Protecting device tracking via Bluetooth address.
- **Security Risks** (Pages 22–27):
  - **Bluesnarfing**: Stealing data (e.g., photos, messages) via Bluetooth.
  - **Bluejacking**: Sending unsolicited spam/phishing messages.
  - **Bluebugging**: Gaining backdoor access to spy or steal data.
  - **Bluesmacking**: DoS attack overwhelming device with large packets.
  - **Car Whispering**: Eavesdropping or injecting audio in Bluetooth-enabled car radios.
- **Statistic**: A 2021 study showed 40–50% of IoT users leave Bluetooth on, increasing vulnerability.

مشاكل أمان البلوتوث: البلوتوث ممكن يتعرض لهجمات زي الـ Bluesnarfing، ودي يعني حد يسرق صورك أو رسايلك. الـ Bluejacking زي لما حد يبعتلك رسايل دعاية مزعجة. الـ Bluebugging لما هكر يدخل جهازك ويبقى يتجسس عليك. الـ Bluesmacking بيحمل الجهاز بداتا كتير عشان يقفل. الـ Car Whispering لما حد يتجسس على مكالماتك في العربية لو فيها بلوتوث. دراسة سنة 2021 قالت إن 40-50% من الناس بيسيبوا البلوتوث شغال، فده بيخليهم عرضة للهجمات.

---

## 12. Security in BLE (Pages 29–32)

### Key Concepts
- **Security Manager Features** (Page 29):
  - **Pairing**: Creating shared secret keys.
  - **Bonding**: Storing keys for future connections.
  - **Authentication**: Verifying shared keys.
  - **Encryption**: Using 128-bit AES for data security.
  - **Message Integrity**: Signing and verifying data beyond CRC checks.
- **Security Addressing** (Page 30):
  - Confidentiality via encryption.
  - Authentication via pairing/bonding.
  - Privacy via resolvable private addresses.
  - Integrity via digital signatures.
- **Security Levels** (Page 32):
  - **Level 1**: No security (no authentication/encryption).
  - **Level 2**: Unauthenticated pairing with encryption.
  - **Level 3**: Authenticated pairing with encryption.
  - **Level 4**: Authenticated LE Secure Connections with encryption.

أمان الـ BLE: الـ Security Manager هو اللي بيدير أمان الاتصال في البلوتوث LE. الـ Pairing بيعمل مفتاح سري بين جهازين، الـ Bonding بيحفظ المفتاح ده للمرات الجاية. الـ Authentication بيتأكد إن الجهازين بيستخدموا نفس المفتاح. الـ Encryption بيستخدم AES-128 عشان يحمي البيانات. الـ Message Integrity بيتأكد إن البيانات متغيرتش. فيه 4 مستويات أمان: من غير أمان خالص (Level 1) لحد أمان عالي جداً (Level 4) بتشفير قوي.

---