\documentclass[10pt,landscape,a4paper]{article}
\usepackage[margin=0.5in]{geometry}
\usepackage{multicol}
\usepackage{fancyhdr}
\usepackage{xcolor}
\usepackage{tcolorbox}
\usepackage{fontspec}
\usepackage{polyglossia}
\usepackage{array}
\usepackage{longtable}

% Set up Arabic support
\setdefaultlanguage{english}
\setotherlanguage{arabic}
\newfontfamily\arabicfont[Script=Arabic]{Arial Unicode MS}

% Define colors
\definecolor{headercolor}{RGB}{51, 102, 153}
\definecolor{sectioncolor}{RGB}{68, 114, 196}
\definecolor{lightblue}{RGB}{217, 225, 242}

% Custom box style
\tcbset{
    sectionbox/.style={
        colback=lightblue,
        colframe=sectioncolor,
        boxrule=1pt,
        arc=3pt,
        left=3pt,
        right=3pt,
        top=3pt,
        bottom=3pt,
        fonttitle=\bfseries\small,
        title style={colback=sectioncolor, coltext=white}
    }
}

% Header setup
\pagestyle{fancy}
\fancyhf{}
\fancyhead[C]{\textbf{\color{headercolor}Bluetooth Technology Part II Study Guide}}
\renewcommand{\headrulewidth}{2pt}
\renewcommand{\headrule}{\hbox to\headwidth{\color{headercolor}\leaders\hrule height \headrulewidth\hfill}}

% Remove page numbers
\pagenumbering{gobble}

\begin{document}

% PAGE 1: Sections 1-6 in 3x2 grid
\begin{multicols}{3}

% Section 1
\begin{tcolorbox}[sectionbox, title=1. Introduction to Bluetooth]
\textbf{Key Concepts:}
\begin{itemize}
\item Wireless technology for short-range communication
\item Part II lecture by Prof. K. Amin (IoT course)
\item Advanced topics: history, specs, architecture, security
\end{itemize}

\begin{arabic}
البلوتوث تقنية لاسلكية لربط أجهزة قريبة بدون أسلاك. مثل نقل صور أو ربط سماعات. المحاضرة تتعمق في التفاصيل التقنية والتاريخ والأمان.
\end{arabic}
\end{tcolorbox}

% Section 2
\begin{tcolorbox}[sectionbox, title=2. Brief History of Bluetooth]
\textbf{Key Milestones:}
\begin{itemize}
\item \textbf{1999:} Basic Rate (BR)
\item \textbf{EDR:} 3x speed improvement
\item \textbf{2009 HS:} Wi-Fi channels for speed
\item \textbf{LE:} Energy efficiency for IoT
\item \textbf{2016-17:} Mesh topology
\end{itemize}

\begin{arabic}
بدأ 1999 بوظائف أساسية، ثم EDR للسرعة، HS للواي فاي، LE لتوفير الطاقة، وMesh للشبكات المترابطة.
\end{arabic}
\end{tcolorbox}

% Section 3
\begin{tcolorbox}[sectionbox, title=3. BED vs. BLE Comparison]
\textbf{Key Differences:}
\begin{itemize}
\item \textbf{BED:} 79 channels, 1-3 Mbps, 7 slaves
\item \textbf{BLE:} 40 channels, 1 Mbps, unlimited slaves
\item \textbf{BLE:} Better privacy, lower power
\item \textbf{Range:} BED 30m, BLE 50m
\end{itemize}

\begin{arabic}
BED أسرع وقنوات أكثر للصوت. BLE يوفر طاقة وخصوصية أفضل وعدد غير محدود من الأجهزة.
\end{arabic}
\end{tcolorbox}

\columnbreak

% Section 4
\begin{tcolorbox}[sectionbox, title=4. BLE Architecture]
\textbf{Protocol Stack:}
\begin{itemize}
\item \textbf{Host:} Applications, GAP, GATT, ATT, SM, L2CAP
\item \textbf{Controller:} HCI, Link Layer, Physical Layer
\item \textbf{GAP:} Device interaction \& roles
\item \textbf{GATT:} Data exchange management
\item \textbf{SM:} Security \& encryption
\end{itemize}

\begin{arabic}
مقسم لـ Host (التطبيقات) و Controller (الاتصال المادي). GAP يحدد التفاعل، GATT يدير البيانات، SM يؤمن الاتصال.
\end{arabic}
\end{tcolorbox}

% Section 5
\begin{tcolorbox}[sectionbox, title=5. BLE RF Channels]
\textbf{Channel Structure:}
\begin{itemize}
\item \textbf{Total:} 40 RF channels, 2 MHz spacing
\item \textbf{Range:} 2402-2480 MHz
\item \textbf{Primary Advertising:} Channels 37, 38, 39
\item \textbf{Data/Secondary:} Remaining 37 channels
\end{itemize}

\begin{arabic}
40 قناة بفاصل 2 ميجا هرتز. 3 قنوات (37-39) للاكتشاف، 37 قناة للبيانات والإعلانات الإضافية.
\end{arabic}
\end{tcolorbox}

% Section 6
\begin{tcolorbox}[sectionbox, title=6. Link Layer States]
\textbf{State Transitions:}
\begin{itemize}
\item \textbf{Standby:} No transmission/reception
\item \textbf{Advertising:} Sends discovery packets
\item \textbf{Scanning:} Listens for advertisements
\item \textbf{Initiating:} Attempts connection
\item \textbf{Connected:} Data exchange (Master/Slave)
\end{itemize}

\begin{arabic}
Standby (نائم)، Advertising (إعلان)، Scanning (بحث)، Initiating (ربط)، Connected (متصل). المبادر Master والمعلن Slave.
\end{arabic}
\end{tcolorbox}

\end{multicols}

\newpage

% PAGE 2: Sections 7-12 in 3x2 grid
\begin{multicols}{3}

% Section 7
\begin{tcolorbox}[sectionbox, title=7. Device Types \& Roles]
\textbf{Four Device Types:}
\begin{itemize}
\item \textbf{Broadcaster:} Transmit only, reduced hardware
\item \textbf{Observer:} Receive only, reduced hardware
\item \textbf{Peripheral:} Bi-directional, full BLE stack
\item \textbf{Central:} Bi-directional, full BLE stack
\end{itemize}

\begin{arabic}
Broadcaster (إرسال فقط)، Observer (استقبال فقط)، Peripheral و Central (إرسال واستقبال بسوفتوير كامل).
\end{arabic}
\end{tcolorbox}

% Section 8
\begin{tcolorbox}[sectionbox, title=8. Generic Access Profile (GAP)]
\textbf{GAP Functions:}
\begin{itemize}
\item \textbf{Modes \& Roles:} Device interaction rules
\item \textbf{Advertisements:} Discovery parameters
\item \textbf{Connection:} Establishment procedures
\item \textbf{Security:} Pairing \& authentication
\end{itemize}

\begin{arabic}
قوانين تفاعل أجهزة BLE. يحدد الأدوار والإعلان والبحث والربط والأمان.
\end{arabic}
\end{tcolorbox}

% Section 9
\begin{tcolorbox}[sectionbox, title=9. Advertising \& Scanning]
\textbf{Discovery Process:}
\begin{itemize}
\item \textbf{Advertising Event:} Transmit on channels 37-39
\item \textbf{Passive Scanning:} Listen without response
\item \textbf{Active Scanning:} SCAN\_REQ → SCAN\_RSP
\item \textbf{Timing:} Scan interval/window overlap required
\end{itemize}

\begin{arabic}
الإعلان على القنوات 37-39. المسح السلبي (استماع فقط) والنشط (طلب معلومات إضافية). يجب تداخل التوقيت للاكتشاف.
\end{arabic}
\end{tcolorbox}

\columnbreak

% Section 10
\begin{tcolorbox}[sectionbox, title=10. Connection Procedure]
\textbf{Connection Process:}
\begin{itemize}
\item \textbf{Host A:} Central/Master, GATT Client
\item \textbf{Host B:} Peripheral/Slave, GATT Server
\item \textbf{Flow:} Standby → Advertising → Initiating → Connected
\end{itemize}

\begin{arabic}
Central يبدأ الاتصال ويصبح Master، Peripheral يعلن ويصبح Slave. التدفق من الانتظار للإعلان للربط للاتصال.
\end{arabic}
\end{tcolorbox}

% Section 11
\begin{tcolorbox}[sectionbox, title=11. Bluetooth Security Concerns]
\textbf{Security Risks:}
\begin{itemize}
\item \textbf{Bluesnarfing:} Data theft (photos, messages)
\item \textbf{Bluejacking:} Spam/phishing messages
\item \textbf{Bluebugging:} Backdoor access/spying
\item \textbf{Bluesmacking:} DoS with large packets
\item \textbf{Car Whispering:} Audio eavesdropping
\end{itemize}

\begin{arabic}
مخاطر: سرقة البيانات، رسائل مزعجة، تجسس، إغراق النظام، التنصت على المكالمات. 40-50\% يتركون البلوتوث مفتوحاً.
\end{arabic}
\end{tcolorbox}

% Section 12
\begin{tcolorbox}[sectionbox, title=12. Security in BLE]
\textbf{Security Features:}
\begin{itemize}
\item \textbf{Pairing:} Shared secret keys
\item \textbf{Bonding:} Store keys for future
\item \textbf{Authentication:} Verify shared keys
\item \textbf{Encryption:} 128-bit AES
\item \textbf{Levels:} 1 (none) → 4 (authenticated LE secure)
\end{itemize}

\begin{arabic}
Security Manager يدير الأمان. Pairing للمفاتيح، Bonding للحفظ، Authentication للتحقق، تشفير AES-128. 4 مستويات أمان.
\end{arabic}
\end{tcolorbox}

\end{multicols}

\end{document}
